import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Users, MapPin, Clock, Star } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import FloatingNavbar from "@/components/FloatingNavbar";

const Career = () => {
  const jobOpenings = [
    {
      title: "Senior Marketing Manager",
      department: "Marketing",
      location: "Remote",
      type: "Full-time",
      experience: "5+ years",
      description: "Lead our marketing initiatives and drive growth strategies for FoodHub's expansion."
    },
    {
      title: "Product Designer",
      department: "Design",
      location: "New York, NY",
      type: "Full-time",
      experience: "3+ years",
      description: "Create intuitive user experiences for our food delivery platform."
    },
    {
      title: "Full Stack Developer",
      department: "Engineering",
      location: "San Francisco, CA",
      type: "Full-time",
      experience: "4+ years",
      description: "Build scalable solutions for our growing food delivery ecosystem."
    },
    {
      title: "Business Development Associate",
      department: "Sales",
      location: "Chicago, IL",
      type: "Full-time",
      experience: "2+ years",
      description: "Expand our restaurant partnerships and drive business growth."
    }
  ];

  const benefits = [
    "Competitive salary and equity",
    "Comprehensive health insurance",
    "Flexible work arrangements",
    "Professional development budget",
    "Unlimited PTO policy",
    "Team building events"
  ];

  return (
    <div className="min-h-screen bg-white">
      <FloatingNavbar />

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-7xl font-black text-gray-900 leading-tight mb-6">
              Where Food Delivery
              <br />
              <span className="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">
                Careers are Built.
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              We bring ideas to life by combining years of experience of our very talented team.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gray-900 text-white hover:bg-gray-800">
                Build Career
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
              <Button size="lg" variant="outline">
                Contact us
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
            
            {/* Left Content */}
            <div className="space-y-8">
              <div>
                <span className="text-sm font-semibold text-primary-600 bg-primary-50 px-3 py-1 rounded-full">
                  Life at FoodHub
                </span>
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mt-4 mb-6">
                  Efficiently transform your
                  <br />
                  candidate experience.
                </h2>
                <p className="text-lg text-gray-600 leading-relaxed">
                  Join our mission to revolutionize food delivery. We're building the future of how people 
                  connect with their favorite restaurants, and we need passionate individuals to help us grow.
                </p>
              </div>

              <div className="flex items-center space-x-4">
                <Button variant="outline" className="flex items-center space-x-2">
                  <Users className="w-4 h-4" />
                  <span>Employees</span>
                </Button>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Star className="w-4 h-4" />
                  <span>Partners</span>
                </Button>
              </div>
            </div>

            {/* Right Content - Team Showcase */}
            <div className="relative">
              <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl p-8 min-h-[400px] relative overflow-hidden">
                {/* Team Member Cards */}
                <div className="absolute top-8 right-8 bg-white rounded-2xl p-4 shadow-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full"></div>
                    <div>
                      <p className="font-semibold text-sm">Sarah Johnson</p>
                      <p className="text-xs text-gray-500">Product Manager</p>
                    </div>
                  </div>
                </div>

                <div className="absolute bottom-8 left-8 bg-white rounded-2xl p-4 shadow-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-secondary-500 to-accent-500 rounded-full"></div>
                    <div>
                      <p className="font-semibold text-sm">Mike Chen</p>
                      <p className="text-xs text-gray-500">Lead Developer</p>
                    </div>
                  </div>
                </div>

                <div className="absolute top-1/2 left-4 bg-white rounded-2xl p-3 shadow-lg">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-accent-500 to-primary-500 rounded-full"></div>
                    <div>
                      <p className="font-semibold text-xs">Alex Rivera</p>
                      <p className="text-xs text-gray-500">Designer</p>
                    </div>
                  </div>
                </div>

                {/* Central Content */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-lg font-semibold text-gray-600">Join Our Team</p>
                    <p className="text-sm text-gray-500">Building the future together</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Job Openings Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Current Openings
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Take advantage of our global employment products to hire who you want, wherever you want - starting in just minutes.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {jobOpenings.map((job, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 group">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{job.title}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        {job.location}
                      </span>
                      <span className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {job.type}
                      </span>
                    </div>
                  </div>
                  <span className="bg-primary-50 text-primary-600 px-3 py-1 rounded-full text-sm font-medium">
                    {job.department}
                  </span>
                </div>
                
                <p className="text-gray-600 mb-4">{job.description}</p>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Experience: {job.experience}</span>
                  <Button variant="outline" size="sm" className="group-hover:bg-primary-600 group-hover:text-white transition-colors">
                    Apply Now
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Why Join FoodHub?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              We believe in taking care of our team members and providing an environment where everyone can thrive.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center p-6 rounded-2xl bg-gradient-to-br from-primary-50 to-secondary-50 hover:shadow-lg transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Star className="w-6 h-6 text-white" />
                </div>
                <p className="font-semibold text-gray-900">{benefit}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-primary-500 to-secondary-500">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Build Your Career?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Join our team and help us revolutionize the food delivery industry.
          </p>
          <Button size="lg" className="bg-white text-primary-600 hover:bg-gray-100">
            View All Positions
            <ArrowRight className="ml-2 w-4 h-4" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-gray-900">
        <div className="container mx-auto text-center">
          <Link to="/" className="text-2xl font-bold text-white mb-4 inline-block">
            FoodHub
          </Link>
          <div className="flex justify-center space-x-8 mb-8">
            <Link to="/" className="text-gray-400 hover:text-white transition-colors">Home</Link>
            <Link to="/career" className="text-white">Career</Link>
            <Link to="/partner" className="text-gray-400 hover:text-white transition-colors">Partner</Link>
            <Link to="/about" className="text-gray-400 hover:text-white transition-colors">About</Link>
            <Link to="/contact" className="text-gray-400 hover:text-white transition-colors">Contact</Link>
          </div>
          <p className="text-gray-400">© 2024 FoodHub. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default Career;
