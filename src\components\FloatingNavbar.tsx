import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Leaf, Menu, X, Sparkles } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

const FloatingNavbar = () => {
  const [isFloatingNav, setIsFloatingNav] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // For home page, show floating nav when scrolled past hero section
      // For other pages, always show the floating nav
      if (location.pathname === '/') {
        setIsFloatingNav(currentScrollY > window.innerHeight * 0.8);
      } else {
        setIsFloatingNav(true);
      }

      // Close mobile menu when scrolling
      if (isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    // Initial check for non-home pages
    if (location.pathname !== '/') {
      setIsFloatingNav(true);
    } else {
      setIsFloatingNav(false);
    }

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMobileMenuOpen, location.pathname]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  // Scroll to top when location changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  const navItems = [
    { name: 'Career', path: '/career' },
    { name: 'Partner', path: '/partner' },
    { name: 'About', path: '/about' },
    { name: 'Contact', path: '/contact' }
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ease-out ${
        isFloatingNav
          ? 'translate-y-0 opacity-100'
          : '-translate-y-full opacity-0'
      }`}
    >
      <div className="mx-auto max-w-3xl px-2 sm:px-4 mt-2 sm:mt-4">
        <div className={`bg-white/95 backdrop-blur-xl rounded-full border border-white/20 px-3 sm:px-6 py-2 sm:py-3 transition-all duration-500 ${
            isFloatingNav
              ? 'shadow-lg shadow-black/10 scale-100'
              : 'shadow-none scale-95'
          }`}>
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2 sm:space-x-3">
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg sm:rounded-xl flex items-center justify-center shadow-sm">
                <Leaf className="w-3 h-3 sm:w-5 sm:h-5 text-white" />
              </div>
              <span className="text-sm sm:text-lg font-bold text-gray-800">FoodHub</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-1">
              <Link to="/">
                <Button
                  variant="ghost"
                  size="sm"
                  className={`rounded-full px-4 py-2 text-sm transition-colors duration-200 ${
                    isActive('/') 
                      ? 'bg-primary-100 text-primary-700' 
                      : 'text-gray-600 hover:bg-primary-50 hover:text-primary-700'
                  }`}
                >
                  Home
                </Button>
              </Link>
              {navItems.map((item) => (
                <Link key={item.path} to={item.path}>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`rounded-full px-4 py-2 text-sm transition-colors duration-200 ${
                      isActive(item.path) 
                        ? 'bg-primary-100 text-primary-700' 
                        : 'text-gray-600 hover:bg-primary-50 hover:text-primary-700'
                    }`}
                  >
                    {item.name}
                  </Button>
                </Link>
              ))}
              <div className="flex items-center ml-4">
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white rounded-full px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <Sparkles className="w-2.5 h-2.5 sm:w-3 sm:h-3 mr-1" />
                  <span className="hidden sm:inline">Get Started</span>
                  <span className="sm:hidden">Start</span>
                </Button>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden p-2 hover:bg-gray-100 rounded-full transition-all duration-200"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <div className="relative w-5 h-5 flex items-center justify-center">
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5 text-gray-700 transition-transform duration-200 rotate-0" />
                ) : (
                  <Menu className="w-5 h-5 text-gray-700 transition-transform duration-200" />
                )}
              </div>
            </Button>
          </div>

          {/* Mobile Menu Overlay */}
          {isMobileMenuOpen && (
            <>
              {/* Backdrop */}
              <div
                className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden animate-fade-in"
                onClick={() => setIsMobileMenuOpen(false)}
              />

              {/* Mobile Menu Panel */}
              <div className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-50 md:hidden animate-slide-in-right mobile-menu">
                <div className="flex flex-col h-full">
                  {/* Header */}
                  <div className="flex items-center justify-between p-6 border-b border-gray-100">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                        <Leaf className="w-5 h-5 text-white" />
                      </div>
                      <span className="text-lg font-bold text-gray-800">FoodHub</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-2"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <X className="w-5 h-5" />
                    </Button>
                  </div>

                  {/* Navigation Links */}
                  <div className="flex-1 py-6">
                    <nav className="space-y-2 px-6">
                      <Link
                        to="/"
                        onClick={() => setIsMobileMenuOpen(false)}
                        className={`mobile-menu-item touch-feedback px-4 py-3 rounded-xl transition-all duration-200 ${
                          isActive('/')
                            ? 'bg-primary-100 text-primary-700 shadow-sm'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        }`}
                      >
                        <span className="font-medium">Home</span>
                        {isActive('/') && (
                          <div className="ml-auto w-2 h-2 bg-primary-500 rounded-full"></div>
                        )}
                      </Link>

                      {navItems.map((item) => (
                        <Link
                          key={item.path}
                          to={item.path}
                          onClick={() => setIsMobileMenuOpen(false)}
                          className={`mobile-menu-item touch-feedback px-4 py-3 rounded-xl transition-all duration-200 ${
                            isActive(item.path)
                              ? 'bg-primary-100 text-primary-700 shadow-sm'
                              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                          }`}
                        >
                          <span className="font-medium">{item.name}</span>
                          {isActive(item.path) && (
                            <div className="ml-auto w-2 h-2 bg-primary-500 rounded-full"></div>
                          )}
                        </Link>
                      ))}
                    </nav>
                  </div>

                  {/* Footer CTA */}
                  <div className="p-6 border-t border-gray-100">
                    <Button
                      className="w-full bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white rounded-xl py-3 shadow-lg hover:shadow-xl transition-all duration-200"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <Sparkles className="w-4 h-4 mr-2" />
                      Get Started
                    </Button>

                    {/* Additional Info */}
                    <div className="mt-4 text-center">
                      <p className="text-xs text-gray-500">
                        Join thousands of satisfied customers
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

export default FloatingNavbar;
