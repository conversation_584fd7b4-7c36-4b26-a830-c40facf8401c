import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Leaf, Menu, X, Sparkles } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

const FloatingNavbar = () => {
  const [isFloatingNav, setIsFloatingNav] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      // Show floating nav when scrolled past hero section (approximately 80vh)
      setIsFloatingNav(currentScrollY > window.innerHeight * 0.8);
      // Close mobile menu when scrolling
      if (isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMobileMenuOpen]);

  const navItems = [
    { name: 'Career', path: '/career' },
    { name: 'Partner', path: '/partner' },
    { name: 'About', path: '/about' },
    { name: 'Contact', path: '/contact' }
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ease-out ${
        isFloatingNav
          ? 'translate-y-0 opacity-100'
          : '-translate-y-full opacity-0'
      }`}
    >
      <div className="mx-auto max-w-3xl px-4 mt-4">
        <div className={`bg-white/95 backdrop-blur-xl rounded-full border border-white/20 px-6 py-3 transition-all duration-500 ${
            isFloatingNav
              ? 'shadow-lg shadow-black/10 scale-100'
              : 'shadow-none scale-95'
          }`}>
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center shadow-sm">
                <Leaf className="w-5 h-5 text-white" />
              </div>
              <span className="text-lg font-bold text-gray-800">FoodHub</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-1">
              <Link to="/">
                <Button
                  variant="ghost"
                  size="sm"
                  className={`rounded-full px-4 py-2 text-sm transition-colors duration-200 ${
                    isActive('/') 
                      ? 'bg-primary-100 text-primary-700' 
                      : 'text-gray-600 hover:bg-primary-50 hover:text-primary-700'
                  }`}
                >
                  Home
                </Button>
              </Link>
              {navItems.map((item) => (
                <Link key={item.path} to={item.path}>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`rounded-full px-4 py-2 text-sm transition-colors duration-200 ${
                      isActive(item.path) 
                        ? 'bg-primary-100 text-primary-700' 
                        : 'text-gray-600 hover:bg-primary-50 hover:text-primary-700'
                    }`}
                  >
                    {item.name}
                  </Button>
                </Link>
              ))}
              <div className="flex items-center ml-4">
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white rounded-full px-4 py-2 text-sm shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <Sparkles className="w-3 h-3 mr-1" />
                  Get Started
                </Button>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden p-2"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </Button>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden mt-4 pt-4 border-t border-gray-100 animate-fade-in">
              <div className="flex flex-col space-y-2">
                <Link to="/">
                  <Button
                    variant="ghost"
                    className={`justify-start w-full transition-colors duration-200 ${
                      isActive('/') 
                        ? 'bg-primary-100 text-primary-700' 
                        : 'text-gray-600 hover:bg-primary-50 hover:text-primary-700'
                    }`}
                  >
                    Home
                  </Button>
                </Link>
                {navItems.map((item) => (
                  <Link key={item.path} to={item.path}>
                    <Button
                      variant="ghost"
                      className={`justify-start w-full transition-colors duration-200 ${
                        isActive(item.path) 
                          ? 'bg-primary-100 text-primary-700' 
                          : 'text-gray-600 hover:bg-primary-50 hover:text-primary-700'
                      }`}
                    >
                      {item.name}
                    </Button>
                  </Link>
                ))}
                <div className="flex justify-center pt-2">
                  <Button size="sm" className="bg-gradient-to-r from-primary-600 to-secondary-600 text-white w-full">
                    <Sparkles className="w-3 h-3 mr-1" />
                    Get Started
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default FloatingNavbar;
