@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom responsive utilities */
@layer utilities {
  /* Mobile-first responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm md:text-base;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base md:text-lg;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg md:text-xl;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl;
  }

  /* Responsive spacing */
  .spacing-responsive {
    @apply space-y-4 sm:space-y-6 md:space-y-8;
  }

  .padding-responsive {
    @apply px-4 sm:px-6 md:px-8 lg:px-12;
  }

  .margin-responsive {
    @apply mb-4 sm:mb-6 md:mb-8;
  }

  /* Responsive grid gaps */
  .gap-responsive {
    @apply gap-4 sm:gap-6 md:gap-8 lg:gap-12;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}

@keyframes slide-out-right {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

.animate-slide-out-right {
  animation: slide-out-right 0.3s ease-in;
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Ensure text doesn't overflow on small screens */
  h1, h2, h3, h4, h5, h6 {
    word-wrap: break-word;
    hyphens: auto;
  }

  /* Improve button spacing on mobile */
  .btn-mobile {
    @apply px-4 py-2 text-sm;
  }

  /* Better mobile navigation */
  .mobile-nav {
    @apply fixed inset-x-0 top-0 z-50;
  }

  /* Mobile menu improvements */
  .mobile-menu-item {
    min-height: 48px; /* Minimum touch target size */
    display: flex;
    align-items: center;
  }

  /* Prevent text selection in mobile menu */
  .mobile-menu {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Smooth scrolling for mobile */
  html {
    scroll-behavior: smooth;
  }

  /* Better touch feedback */
  .touch-feedback {
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
  }
}

/* Tablet-specific improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Optimize for tablet layouts */
  .tablet-grid {
    @apply grid-cols-2;
  }

  .tablet-text {
    @apply text-lg;
  }
}

/* Desktop improvements */
@media (min-width: 1025px) {
  /* Enhance desktop experience */
  .desktop-grid {
    @apply grid-cols-3;
  }

  .desktop-spacing {
    @apply space-y-8;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}