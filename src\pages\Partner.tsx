import { Button } from "@/components/ui/button";
import { ArrowRight, Store, TrendingUp, Users, Shield, Clock, Star } from "lucide-react";
import { Link } from "react-router-dom";
import FloatingNavbar from "@/components/FloatingNavbar";

const Partner = () => {
  const partnerBenefits = [
    {
      icon: TrendingUp,
      title: "Increase Revenue",
      description: "Boost your sales by reaching more customers through our platform"
    },
    {
      icon: Users,
      title: "Expand Customer Base",
      description: "Connect with thousands of hungry customers in your area"
    },
    {
      icon: Shield,
      title: "Secure Payments",
      description: "Get paid quickly and securely with our trusted payment system"
    },
    {
      icon: Clock,
      title: "24/7 Support",
      description: "Our dedicated support team is here to help you succeed"
    }
  ];

  const partnerTypes = [
    {
      title: "Restaurant Partners",
      description: "Join thousands of restaurants already growing with FoodHub",
      features: ["Commission-based pricing", "Marketing support", "Analytics dashboard", "Delivery management"],
      cta: "Become a Restaurant Partner"
    },
    {
      title: "Delivery Partners",
      description: "Earn money on your schedule as a FoodHub delivery driver",
      features: ["Flexible hours", "Competitive rates", "Weekly payments", "Driver app support"],
      cta: "Start Delivering Today"
    },
    {
      title: "Corporate Partners",
      description: "Partner with us to provide meal solutions for your employees",
      features: ["Bulk ordering", "Corporate accounts", "Expense management", "Custom solutions"],
      cta: "Explore Corporate Solutions"
    }
  ];

  const stats = [
    { value: "10,000+", label: "Restaurant Partners" },
    { value: "50,000+", label: "Active Drivers" },
    { value: "1M+", label: "Orders Delivered" },
    { value: "95%", label: "Partner Satisfaction" }
  ];

  return (
    <div className="min-h-screen bg-white">
      <FloatingNavbar />

      {/* Hero Section */}
      <section className="pt-32 pb-16 px-4 bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-7xl font-black text-gray-900 leading-tight mb-6">
              Grow Your Business
              <br />
              <span className="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">
                With FoodHub
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Join thousands of successful partners who are growing their business with our platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gray-900 text-white hover:bg-gray-800">
                Become a Partner
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
              <Button size="lg" variant="outline">
                Learn More
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl md:text-5xl font-black text-primary-600 mb-2">{stat.value}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Why Partner With Us?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              We provide the tools and support you need to succeed in the food delivery business.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {partnerBenefits.map((benefit, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 text-center hover:shadow-lg transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <benefit.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partner Types Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Choose Your Partnership
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Whether you're a restaurant, driver, or corporate client, we have the right partnership for you.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {partnerTypes.map((type, index) => (
              <div key={index} className="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-3xl p-8 hover:shadow-lg transition-all duration-300">
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{type.title}</h3>
                  <p className="text-gray-600 mb-6">{type.description}</p>
                </div>

                <div className="space-y-3 mb-8">
                  {type.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center">
                      <div className="w-2 h-2 bg-primary-500 rounded-full mr-3"></div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <Button className="w-full bg-gray-900 text-white hover:bg-gray-800">
                  {type.cta}
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Partner Success Stories
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how our partners are growing their businesses with FoodHub.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mr-4">
                  <Store className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h4 className="font-bold text-gray-900">Mario's Pizza</h4>
                  <p className="text-sm text-gray-500">Restaurant Partner</p>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                "Since joining FoodHub, our delivery orders have increased by 300%. The platform is easy to use and the support team is fantastic."
              </p>
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
            </div>

            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-secondary-500 to-accent-500 rounded-full flex items-center justify-center mr-4">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h4 className="font-bold text-gray-900">Sarah Johnson</h4>
                  <p className="text-sm text-gray-500">Delivery Driver</p>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                "I love the flexibility FoodHub offers. I can work when I want and the earnings are great. The app is user-friendly too."
              </p>
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
            </div>

            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-accent-500 to-primary-500 rounded-full flex items-center justify-center mr-4">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h4 className="font-bold text-gray-900">TechCorp Inc.</h4>
                  <p className="text-sm text-gray-500">Corporate Partner</p>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                "FoodHub's corporate solutions have made lunch ordering for our 500+ employees seamless. Great service and variety."
              </p>
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-primary-500 to-secondary-500">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Partner With Us?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Join thousands of successful partners and start growing your business today.
          </p>
          <Button size="lg" className="bg-white text-primary-600 hover:bg-gray-100">
            Get Started Now
            <ArrowRight className="ml-2 w-4 h-4" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-gray-900">
        <div className="container mx-auto text-center">
          <Link to="/" className="text-2xl font-bold text-white mb-4 inline-block">
            FoodHub
          </Link>
          <div className="flex justify-center space-x-8 mb-8">
            <Link to="/" className="text-gray-400 hover:text-white transition-colors">Home</Link>
            <Link to="/career" className="text-gray-400 hover:text-white transition-colors">Career</Link>
            <Link to="/partner" className="text-white">Partner</Link>
            <Link to="/about" className="text-gray-400 hover:text-white transition-colors">About</Link>
            <Link to="/contact" className="text-gray-400 hover:text-white transition-colors">Contact</Link>
          </div>
          <p className="text-gray-400">© 2024 FoodHub. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default Partner;
