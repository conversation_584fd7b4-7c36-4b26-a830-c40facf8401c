import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Leaf, Users, Truck, Heart, Award, Globe } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import FloatingNavbar from "@/components/FloatingNavbar";
import BottomBlurOverlay from "@/components/BottomBlurOverlay";

const About = () => {
  const values = [
    {
      icon: Heart,
      title: "Customer First",
      description: "We put our customers at the center of everything we do, ensuring exceptional experiences."
    },
    {
      icon: Leaf,
      title: "Sustainability",
      description: "Committed to sustainable practices that benefit our planet and communities."
    },
    {
      icon: Users,
      title: "Community",
      description: "Building strong relationships with restaurants, drivers, and customers alike."
    },
    {
      icon: Award,
      title: "Excellence",
      description: "Striving for excellence in service, technology, and innovation."
    }
  ];

  const team = [
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      description: "Former tech executive with 15+ years in food industry innovation."
    },
    {
      name: "<PERSON>",
      role: "CTO",
      description: "Leading our technology vision with expertise in scalable platforms."
    },
    {
      name: "<PERSON>",
      role: "Head of Operations",
      description: "Ensuring smooth operations across all our delivery networks."
    },
    {
      name: "<PERSON>",
      role: "Head of Marketing",
      description: "Building brand awareness and customer engagement strategies."
    }
  ];

  const milestones = [
    { year: "2020", event: "FoodHub Founded", description: "Started with a vision to revolutionize food delivery" },
    { year: "2021", event: "1,000 Restaurants", description: "Reached our first major milestone of partner restaurants" },
    { year: "2022", event: "Series A Funding", description: "Secured $10M to expand operations nationwide" },
    { year: "2023", event: "1M+ Orders", description: "Delivered our millionth order to happy customers" },
    { year: "2024", event: "Global Expansion", description: "Launched in 5 new countries across 3 continents" }
  ];

  return (
    <div className="min-h-screen bg-white">
      <FloatingNavbar />

      {/* Hero Section */}
      <section className="pt-32 pb-16 px-4 bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-7xl font-black text-gray-900 leading-tight mb-6">
              Connecting Communities
              <br />
              <span className="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">
                Through Food
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              We're on a mission to make delicious food accessible to everyone while supporting local restaurants and creating opportunities for drivers.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gray-900 text-white hover:bg-gray-800">
                Our Story
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
              <Button size="lg" variant="outline">
                Join Our Mission
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            
            {/* Left Content */}
            <div className="space-y-8">
              <div>
                <h2 className="text-6xl md:text-7xl lg:text-8xl font-black text-gray-900 leading-none mb-4">
                  sustainable
                  <br />
                  <span className="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">
                    food delivery
                  </span>
                </h2>
                
                <div className="relative">
                  <div className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-primary-400/20 to-secondary-400/20 rounded-full blur-xl"></div>
                  <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-lg relative z-10">
                    Welcome to a revolutionary journey that transcends traditional food delivery. 
                    Discover the artistry of sustainable food systems captured through innovation and technology.
                  </p>
                </div>
              </div>

              {/* Social Links */}
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-white rounded-full border-2 border-gray-200 flex items-center justify-center hover:border-primary-400 transition-colors cursor-pointer group">
                  <span className="text-sm font-bold text-gray-600 group-hover:text-primary-600">yt</span>
                </div>
                <div className="w-12 h-12 bg-white rounded-full border-2 border-gray-200 flex items-center justify-center hover:border-primary-400 transition-colors cursor-pointer group">
                  <span className="text-sm font-bold text-gray-600 group-hover:text-primary-600">ig</span>
                </div>
                <div className="w-12 h-12 bg-white rounded-full border-2 border-gray-200 flex items-center justify-center hover:border-primary-400 transition-colors cursor-pointer group">
                  <span className="text-sm font-bold text-gray-600 group-hover:text-primary-600">fb</span>
                </div>
                <div className="w-12 h-12 bg-white rounded-full border-2 border-gray-200 flex items-center justify-center hover:border-primary-400 transition-colors cursor-pointer group">
                  <span className="text-sm font-bold text-gray-600 group-hover:text-primary-600">x</span>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-8">
                <div className="text-center">
                  <div className="text-5xl md:text-6xl font-black text-gray-900 mb-2">+10k</div>
                  <div className="text-sm text-gray-600 font-medium">Restaurants reaching wide</div>
                  <div className="text-sm text-gray-600">audience and creating lasting impression</div>
                </div>
                <div className="text-center">
                  <div className="text-5xl md:text-6xl font-black text-gray-900 mb-2">+1M</div>
                  <div className="text-sm text-gray-600 font-medium">Orders delivered, engaging</div>
                  <div className="text-sm text-gray-600">customers that love our service</div>
                </div>
              </div>
            </div>

            {/* Right Content - Image with Overlays */}
            <div className="relative">
              <div className="relative bg-gradient-to-br from-primary-400 to-secondary-500 rounded-[3rem] p-8 overflow-hidden">
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute top-10 left-10 w-20 h-20 border-2 border-white rounded-full"></div>
                  <div className="absolute bottom-20 right-10 w-16 h-16 border-2 border-white rounded-full"></div>
                  <div className="absolute top-1/2 right-20 w-12 h-12 border-2 border-white rounded-full"></div>
                </div>
                
                {/* Main Image Placeholder */}
                <div className="relative bg-white/10 backdrop-blur-sm rounded-3xl p-8 min-h-[400px] flex items-center justify-center">
                  <div className="text-center text-white">
                    <Globe className="w-24 h-24 mx-auto mb-4 opacity-80" />
                    <p className="text-lg font-semibold opacity-90">Global Food Network</p>
                    <p className="text-sm opacity-70">Connecting Communities</p>
                  </div>
                </div>

                {/* Floating Elements */}
                <div className="absolute top-8 right-8 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-lg animate-float">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center mb-2">
                    <Leaf className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-xs text-gray-600 font-medium">Sustainable</p>
                </div>

                <div className="absolute bottom-8 left-8 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-lg animate-float" style={{ animationDelay: '2s' }}>
                  <div className="w-12 h-12 bg-gradient-to-br from-secondary-500 to-accent-500 rounded-xl flex items-center justify-center mb-2">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-xs text-gray-600 font-medium">Community</p>
                </div>

                <div className="absolute top-1/2 left-4 bg-white/90 backdrop-blur-sm rounded-2xl p-3 shadow-lg animate-float" style={{ animationDelay: '4s' }}>
                  <div className="w-10 h-10 bg-gradient-to-br from-accent-500 to-primary-500 rounded-xl flex items-center justify-center mb-1">
                    <Truck className="w-5 h-5 text-white" />
                  </div>
                  <p className="text-xs text-gray-600 font-medium">Fast Delivery</p>
                </div>

                {/* Arrow Navigation */}
                <div className="absolute bottom-8 right-8 w-12 h-12 bg-gray-900 rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-800 transition-colors">
                  <ArrowRight className="w-5 h-5 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our Values
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The principles that guide everything we do and shape our company culture.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 text-center hover:shadow-lg transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <value.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our Journey
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              From a small startup to a global food delivery platform - here's how we got here.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            {milestones.map((milestone, index) => (
              <div key={index} className="flex items-start mb-12 last:mb-0">
                <div className="flex-shrink-0 w-24 text-right mr-8">
                  <div className="text-2xl font-bold text-primary-600">{milestone.year}</div>
                </div>
                <div className="flex-shrink-0 w-4 h-4 bg-primary-500 rounded-full mt-2 mr-8 relative">
                  {index !== milestones.length - 1 && (
                    <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-0.5 h-12 bg-primary-200"></div>
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{milestone.event}</h3>
                  <p className="text-gray-600">{milestone.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Meet Our Team
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The passionate individuals behind FoodHub's success.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 text-center hover:shadow-lg transition-all duration-300">
                <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full mx-auto mb-6"></div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                <p className="text-primary-600 font-semibold mb-4">{member.role}</p>
                <p className="text-gray-600 text-sm">{member.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-primary-500 to-secondary-500">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Join Our Mission
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Be part of the food delivery revolution. Whether as a partner, team member, or customer.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-primary-600 hover:bg-gray-100">
              Explore Careers
              <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
              Become a Partner
              <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-gray-900">
        <div className="container mx-auto text-center">
          <Link to="/" className="text-2xl font-bold text-white mb-4 inline-block">
            FoodHub
          </Link>
          <div className="flex justify-center space-x-8 mb-8">
            <Link to="/" className="text-gray-400 hover:text-white transition-colors">Home</Link>
            <Link to="/career" className="text-gray-400 hover:text-white transition-colors">Career</Link>
            <Link to="/partner" className="text-gray-400 hover:text-white transition-colors">Partner</Link>
            <Link to="/about" className="text-white">About</Link>
            <Link to="/contact" className="text-gray-400 hover:text-white transition-colors">Contact</Link>
          </div>
          <p className="text-gray-400">© 2024 FoodHub. All rights reserved.</p>
        </div>
      </footer>

      <BottomBlurOverlay />
    </div>
  );
};

export default About;
